{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 45648, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 45648, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 45648, "tid": 638, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 45648, "tid": 638, "ts": 1756864479614589, "dur": 575, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 45648, "tid": 638, "ts": 1756864479618272, "dur": 702, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 45648, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 45648, "tid": 1, "ts": 1756864479361847, "dur": 3974, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756864479365823, "dur": 39302, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 45648, "tid": 1, "ts": 1756864479405133, "dur": 25331, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 45648, "tid": 638, "ts": 1756864479618977, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 45648, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479360271, "dur": 5452, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479365724, "dur": 242144, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479366522, "dur": 2810, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479369336, "dur": 1367, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370706, "dur": 148, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370857, "dur": 9, "ph": "X", "name": "ProcessMessages 20508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370867, "dur": 24, "ph": "X", "name": "ReadAsync 20508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370893, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370916, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370939, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370961, "dur": 36, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479370999, "dur": 3, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371002, "dur": 33, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371038, "dur": 21, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371061, "dur": 82, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371152, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371206, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371208, "dur": 31, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371241, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371243, "dur": 22, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371269, "dur": 24, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371296, "dur": 25, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371323, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371345, "dur": 20, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371368, "dur": 19, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371389, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371412, "dur": 379, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371795, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371858, "dur": 3, "ph": "X", "name": "ProcessMessages 6407", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371863, "dur": 30, "ph": "X", "name": "ReadAsync 6407", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371895, "dur": 31, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371929, "dur": 30, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371961, "dur": 25, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479371988, "dur": 26, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372016, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372019, "dur": 28, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372049, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372051, "dur": 28, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372082, "dur": 31, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372115, "dur": 21, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372138, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372158, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372180, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372207, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372233, "dur": 19, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372254, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372275, "dur": 19, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372303, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372325, "dur": 18, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372344, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372346, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372369, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372394, "dur": 26, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372423, "dur": 19, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372444, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372465, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372484, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372507, "dur": 19, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372528, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372549, "dur": 24, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372575, "dur": 21, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372598, "dur": 17, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372618, "dur": 19, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372638, "dur": 20, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372661, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372690, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372692, "dur": 26, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372720, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372745, "dur": 18, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372765, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372787, "dur": 22, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372811, "dur": 19, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372832, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372853, "dur": 18, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372873, "dur": 18, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372893, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372914, "dur": 22, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372938, "dur": 19, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372959, "dur": 20, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479372981, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373002, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373024, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373045, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373071, "dur": 27, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373100, "dur": 28, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373130, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373150, "dur": 23, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373176, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373198, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373223, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373246, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373267, "dur": 19, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373288, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373307, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373328, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373348, "dur": 129, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373479, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373499, "dur": 28, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373529, "dur": 19, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373550, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373572, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373574, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373600, "dur": 18, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373619, "dur": 18, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373640, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373662, "dur": 21, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373685, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373706, "dur": 18, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373726, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373745, "dur": 18, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373764, "dur": 18, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373784, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373804, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373829, "dur": 19, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373850, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373852, "dur": 26, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373880, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373900, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373921, "dur": 30, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373954, "dur": 19, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373976, "dur": 14, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479373991, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374016, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374035, "dur": 20, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374056, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374077, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374096, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374116, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374135, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374154, "dur": 17, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374173, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374193, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374213, "dur": 18, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374234, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374255, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374275, "dur": 18, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374296, "dur": 18, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374315, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374317, "dur": 17, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374337, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374358, "dur": 21, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374382, "dur": 22, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374405, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374425, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374451, "dur": 27, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374481, "dur": 21, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374504, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374527, "dur": 31, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374560, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374590, "dur": 18, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374611, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374631, "dur": 21, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374654, "dur": 18, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374674, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374694, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374715, "dur": 19, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374736, "dur": 19, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374757, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374778, "dur": 25, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374806, "dur": 28, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374836, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374858, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374879, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374901, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374922, "dur": 22, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374946, "dur": 21, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374970, "dur": 28, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479374999, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375022, "dur": 28, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375052, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375077, "dur": 17, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375096, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375115, "dur": 17, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375134, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375154, "dur": 18, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375174, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375193, "dur": 18, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375213, "dur": 22, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375239, "dur": 24, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375267, "dur": 26, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375296, "dur": 20, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375318, "dur": 23, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375343, "dur": 23, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375368, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375391, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375413, "dur": 20, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375435, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375456, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375477, "dur": 18, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375498, "dur": 26, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375526, "dur": 27, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375555, "dur": 20, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375578, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375599, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375622, "dur": 29, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375653, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375676, "dur": 23, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375704, "dur": 30, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375737, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375761, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375784, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375806, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375827, "dur": 19, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375848, "dur": 23, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375873, "dur": 21, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375896, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375917, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375939, "dur": 30, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479375972, "dur": 26, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376000, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376021, "dur": 22, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376046, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376068, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376089, "dur": 19, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376110, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376132, "dur": 19, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376153, "dur": 19, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376174, "dur": 20, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376196, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376217, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376238, "dur": 19, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376260, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376281, "dur": 18, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376301, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376323, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376344, "dur": 17, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376363, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376384, "dur": 25, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376411, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376413, "dur": 28, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376443, "dur": 19, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376465, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376486, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376506, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376526, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376546, "dur": 23, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376573, "dur": 25, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376601, "dur": 22, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376625, "dur": 20, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376647, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376667, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376688, "dur": 19, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376709, "dur": 20, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376731, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376752, "dur": 20, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376774, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376794, "dur": 28, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376824, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376845, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376846, "dur": 26, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376874, "dur": 19, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376895, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376918, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376947, "dur": 30, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376979, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479376980, "dur": 20, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377002, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377024, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377045, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377065, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377085, "dur": 18, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377104, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377124, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377144, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377164, "dur": 18, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377184, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377205, "dur": 87, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377294, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377295, "dur": 32, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377329, "dur": 1, "ph": "X", "name": "ProcessMessages 1591", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377331, "dur": 21, "ph": "X", "name": "ReadAsync 1591", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377354, "dur": 355, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377714, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479377758, "dur": 402, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378168, "dur": 88, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378259, "dur": 6, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378267, "dur": 46, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378315, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378318, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378359, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378361, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378408, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378411, "dur": 50, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378465, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378467, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378513, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378516, "dur": 56, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378575, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378578, "dur": 62, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378644, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378647, "dur": 51, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378701, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378704, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378759, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378761, "dur": 46, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378811, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378814, "dur": 44, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378861, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378863, "dur": 51, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378917, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378920, "dur": 53, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378976, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479378980, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379034, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379038, "dur": 38, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379078, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379080, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379108, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379151, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379154, "dur": 52, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379209, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379212, "dur": 48, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379264, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379267, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379313, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379314, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379352, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379354, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379402, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379404, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379453, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379456, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379503, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379505, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379536, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379574, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379577, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379619, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379621, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379660, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379662, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379692, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379736, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379765, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379807, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379847, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479379965, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479380009, "dur": 9824, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479389839, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479389874, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479389876, "dur": 126, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390007, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390053, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390100, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390129, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390166, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390169, "dur": 463, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479390793, "dur": 135, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479391058, "dur": 440, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479391501, "dur": 89, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479391632, "dur": 371, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479392006, "dur": 5, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479392012, "dur": 99, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479392480, "dur": 28, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479392601, "dur": 451, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479393064, "dur": 3, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479395861, "dur": 51, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479395915, "dur": 4, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479395920, "dur": 165, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396089, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396092, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396132, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396134, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396173, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396201, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396202, "dur": 93, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396298, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479396320, "dur": 982, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479397305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479397307, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479397342, "dur": 539, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479397884, "dur": 147580, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479545472, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479545475, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479545537, "dur": 1256, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479546795, "dur": 3089, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479549889, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479549913, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479549914, "dur": 1776, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479551693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479551695, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479551732, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479551743, "dur": 46771, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479598523, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479598527, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479598573, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479598576, "dur": 971, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479599553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479599556, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479599605, "dur": 19, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479599625, "dur": 705, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479600333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479600335, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479600366, "dur": 388, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 45648, "tid": 12884901888, "ts": 1756864479600757, "dur": 6491, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 45648, "tid": 638, "ts": 1756864479618986, "dur": 560, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 45648, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 45648, "tid": 8589934592, "ts": 1756864479358141, "dur": 72351, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 45648, "tid": 8589934592, "ts": 1756864479430495, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 45648, "tid": 8589934592, "ts": 1756864479430500, "dur": 4937, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 45648, "tid": 638, "ts": 1756864479619548, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 45648, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 45648, "tid": 4294967296, "ts": 1756864479341362, "dur": 267305, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756864479345374, "dur": 7102, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756864479608805, "dur": 3321, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756864479610758, "dur": 76, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 45648, "tid": 4294967296, "ts": 1756864479612190, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 45648, "tid": 638, "ts": 1756864479619553, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756864479363564, "dur": 2043, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479365617, "dur": 390, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479366083, "dur": 767, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479367913, "dur": 912, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756864479369734, "dur": 1229, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756864479371920, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756864479366871, "dur": 10576, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479377455, "dur": 222280, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479599737, "dur": 192, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479599929, "dur": 184, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479600150, "dur": 64, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479600429, "dur": 1403, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756864479366637, "dur": 10829, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479377482, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756864479377807, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0903FEE7BAED8E66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756864479377862, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479377940, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_4297D6ED2C7F7138.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756864479378094, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479378283, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479378391, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756864479378656, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756864479378797, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756864479379005, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1756864479379249, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756864479379543, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756864479379618, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479380727, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479381641, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479382515, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479383348, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479384192, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479385380, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479386214, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479387305, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479388275, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479388858, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479389657, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479390210, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479390293, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756864479390549, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756864479391345, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479391662, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756864479391873, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756864479392405, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479392626, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479392786, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479393057, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479393422, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479393581, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479394499, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479394557, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479395024, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479395230, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479395758, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756864479396249, "dur": 203511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479366673, "dur": 10807, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479377488, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756864479377969, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479378038, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D83BBE6D171D2E8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756864479378144, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_40502E6DCAE34EBD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756864479378202, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479378271, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756864479378501, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479378570, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_0548B6350D93892B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756864479378754, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756864479378988, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756864479379389, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756864479379540, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756864479379635, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479380729, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479381627, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479382668, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479383533, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479385250, "dur": 1304, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@be6c4fd0abf5\\InputSystem\\Utilities\\ReadOnlyArray.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756864479384834, "dur": 2074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479386908, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479387620, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479388470, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479388842, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479389641, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479390180, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479390344, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756864479390564, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756864479391226, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479391432, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479391569, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479391658, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479392120, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479392205, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756864479392420, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756864479392943, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479393035, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479393098, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479393408, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479393597, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479394537, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479395040, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479395195, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479395757, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756864479396243, "dur": 203524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479366718, "dur": 10772, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479377498, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756864479378134, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479378290, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756864479378445, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756864479378736, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756864479378832, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756864479379121, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756864479379401, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756864479379693, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479380843, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479382263, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479383383, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479384262, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479385418, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479386270, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479387174, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479388117, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479388228, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479388812, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479389622, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479390161, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479390299, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756864479390545, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756864479391206, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479391440, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479391659, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479392119, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479392208, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479392311, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479392428, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479392595, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479393017, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479393075, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1756864479393127, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479393392, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479393580, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479394496, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479394556, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479395022, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479395239, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479395788, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756864479396233, "dur": 203497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479366761, "dur": 10740, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479377509, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_36F2911304CC020F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479378079, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_574DE5121CC9D879.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479378142, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479378297, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479378544, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A964C83E710562E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479378668, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479378725, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756864479379116, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756864479379331, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756864479379592, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756864479379953, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479381126, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479382197, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\WebWindow.cs"}}, {"pid": 12345, "tid": 4, "ts": 1756864479382042, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479383493, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479385167, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479386194, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479387077, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479387916, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479388535, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479388848, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479389660, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479390205, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479390340, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479391393, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479391560, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479391618, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479391677, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479392307, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479392467, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479392627, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479393080, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756864479393140, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479393375, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479393619, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479393731, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756864479394450, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479394551, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479394649, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756864479395069, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479395209, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479395313, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756864479395621, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479395821, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479395913, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756864479396119, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756864479396297, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756864479396400, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756864479398040, "dur": 147542, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756864479549853, "dur": 48705, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756864479549833, "dur": 48727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756864479598580, "dur": 1068, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756864479366797, "dur": 10717, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479377526, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_448E988461C74DB0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756864479378089, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5A472186080A5AB1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756864479378155, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479378270, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479378422, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756864479378513, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_F7EA645C15EDC022.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756864479378662, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756864479378838, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1756864479378935, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756864479379035, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756864479379185, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1756864479379413, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756864479379650, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479380719, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479381605, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479382510, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479383557, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479384927, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479385773, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479386661, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479387502, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479388338, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479388832, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479389637, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479390175, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479390306, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756864479390529, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756864479391230, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479391446, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756864479391640, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756864479392124, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479392305, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479392452, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479392622, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479393082, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1756864479393156, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479393320, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479393565, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479394535, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479394989, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479395180, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479395754, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479396231, "dur": 153606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756864479549874, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1756864479549838, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1756864479550001, "dur": 1791, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1756864479551796, "dur": 47970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479366826, "dur": 10703, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479377539, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_0A94E029953135AC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756864479378011, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479378076, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_C1731F7A9273BA75.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756864479378181, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F3E222EC1821C3F6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756864479378254, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479378544, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BA4F652EDE8ADBA9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756864479378698, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756864479379099, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756864479379301, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1756864479379457, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756864479379544, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756864479379786, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479380793, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479381712, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479382659, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479383566, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479384907, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479385774, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479386663, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479387500, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479388507, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479388854, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479389652, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479390196, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479390334, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479391295, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479391381, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479391449, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479391513, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479391568, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479391706, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479392160, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479392331, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479392450, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479392620, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479393047, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479393465, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479393562, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479394482, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479394565, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479395012, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479395287, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479395779, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756864479396254, "dur": 203526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479366857, "dur": 10699, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479377568, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756864479378029, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479378107, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_7CE7E2E85C9B872F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756864479378168, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479378279, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_25538968F9D76790.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756864479378469, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_25538968F9D76790.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756864479378722, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756864479378894, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1756864479379104, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756864479379337, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479379396, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756864479379647, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479380715, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479381568, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479382485, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479383329, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479384196, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479385393, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479386230, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479387114, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479387968, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479388357, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479388836, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479389638, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479390178, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479390308, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756864479390556, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756864479391458, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479391703, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756864479391870, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756864479392789, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479393054, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479393432, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479393588, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479394544, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479395007, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479395310, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479395752, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479396230, "dur": 41022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756864479437254, "dur": 162533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479367437, "dur": 10366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479377805, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B3B4C94707F7A218.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756864479377912, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_51DEFFE41CB8BCA4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756864479378092, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_D95410EB09159078.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756864479378268, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479378389, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756864479378510, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756864479378592, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_AF814272D3883F57.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756864479379184, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756864479379335, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1756864479379395, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756864479379587, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1756864479379736, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479380984, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479381917, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479382882, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479383776, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479385107, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479385944, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479386819, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479387664, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479388553, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479388844, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479389643, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479390184, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479390349, "dur": 954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479391303, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479391443, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479391566, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479391684, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479392305, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479392402, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479392544, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479392603, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479393051, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479393442, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479393589, "dur": 922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479394543, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479395000, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479395222, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479395799, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756864479396239, "dur": 203503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479367410, "dur": 10380, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479377798, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756864479377875, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479377944, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_20112DFB1B9A6681.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756864479377996, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479378078, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_3A57A969F158AF6F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756864479378133, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479378192, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_21F715593E655655.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756864479378245, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479378726, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_56F11C16920C2AB4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756864479379003, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1756864479379103, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756864479379280, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1756864479379388, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756864479379608, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479380624, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479381537, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479382463, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479383364, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479384281, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479385457, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479386306, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479387172, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479388194, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479388276, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479388855, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479389655, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479390206, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479390343, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479391305, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1756864479391392, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479391488, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479391554, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479391664, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479392120, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479392313, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479392436, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479392619, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479393023, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479393125, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479393382, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479393592, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479394541, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479395032, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479395218, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756864479395332, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756864479395584, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479395785, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479396229, "dur": 39312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479437005, "dur": 235, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 9, "ts": 1756864479435542, "dur": 1704, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756864479437246, "dur": 162556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479366948, "dur": 10654, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479377611, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_470099C400F771FA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756864479378053, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479378125, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_80D3DD5FCF7A4155.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756864479378240, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479378473, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756864479378950, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1756864479379036, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479379089, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1756864479379226, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756864479379390, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756864479379536, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756864479379651, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479380662, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479381536, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479382440, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479383366, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479384323, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479385569, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479386441, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479387327, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479388293, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479388835, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479389635, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479390174, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479390322, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756864479390581, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756864479391363, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479391713, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479392137, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479392206, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479392319, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479392433, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479392609, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479393049, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479393457, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479393606, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479394536, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479394991, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479395181, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479395756, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756864479396241, "dur": 203506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479366983, "dur": 10631, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479377623, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756864479378094, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479378236, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756864479378416, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479378479, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756864479378529, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479378589, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756864479378646, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756864479378777, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1756864479379133, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756864479379183, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479379233, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756864479379431, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756864479379506, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479379559, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756864479379672, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479380681, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479381575, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479382520, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479383465, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479384708, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479385568, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479386441, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479387384, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479388263, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479388813, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479389620, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479390162, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479390287, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756864479390513, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756864479391138, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479391335, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479391387, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479391488, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479391545, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479391639, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479391698, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479392210, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479392304, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479392473, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479392638, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479393038, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479393304, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479393556, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479394485, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479394564, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479395013, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479395279, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479395773, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756864479396263, "dur": 203512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479367036, "dur": 10589, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479377632, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_88B5229BB905F051.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756864479378150, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479378276, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756864479378422, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479378493, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43979216E7A92FE9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756864479378596, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756864479378750, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756864479378846, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756864479379167, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756864479379394, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756864479379841, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479380930, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479382463, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479383345, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479384229, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479385482, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479386352, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479387197, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479388283, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479388818, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479389659, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479390203, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479390329, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479391301, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479391389, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479391544, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479391704, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479392171, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479392327, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479392429, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479392608, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479393059, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479393417, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479393582, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479394501, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479394552, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479395017, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479395262, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479395792, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756864479396238, "dur": 203494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479367077, "dur": 10559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479377643, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7E5B9059DDF5C58D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756864479378128, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479378208, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_71130C2744ADE79E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756864479378270, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479378496, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_2CD26214566796B4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756864479378596, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_2CD26214566796B4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756864479378814, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1756864479379026, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756864479379355, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1756864479379586, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756864479379727, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479380874, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479381747, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479382643, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479383628, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479385230, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479386049, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479386927, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479387278, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479388152, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479388285, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479388820, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479389628, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479390204, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479390331, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479391255, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479391402, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479391579, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479391665, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479392325, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479392427, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479392597, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479392914, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479393049, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479393451, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479393587, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479394547, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479395005, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479395318, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479395755, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756864479396277, "dur": 203492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479367105, "dur": 10544, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479377659, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756864479378155, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479378296, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479378412, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479378682, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756864479378771, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756864479379118, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756864479379249, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756864479379399, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756864479379538, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756864479379595, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479380734, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479381660, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479382655, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479383552, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479384947, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479385799, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479386680, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479387506, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479388390, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479388850, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479389649, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479390191, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479390362, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479391293, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479391382, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479391436, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479391576, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479391660, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479391716, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479392127, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479392204, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756864479392402, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756864479392930, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479393153, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479393331, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479393594, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479394564, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479395003, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479395326, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479395713, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479395764, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756864479396257, "dur": 203532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479367136, "dur": 10529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479377677, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_9520DE7000879E41.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756864479378227, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756864479378285, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479378407, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D52594234CD03E41.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756864479378613, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479378756, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756864479379042, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1756864479379103, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756864479379204, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756864479379401, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756864479379711, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479380784, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479381627, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479382612, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479383489, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479384724, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479385725, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479386619, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479387454, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479388331, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479388828, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479389633, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479390172, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479390285, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756864479390677, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756864479391438, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479391578, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756864479391802, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756864479392254, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479392438, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479392626, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756864479392814, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756864479393187, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479393341, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479393572, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479394492, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479394560, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479395014, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479395271, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479395783, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756864479396256, "dur": 203529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479367162, "dur": 10518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479377691, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_CEA60A69666C96C6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756864479377768, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479377878, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479378203, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B9618BB877B2AF9C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756864479378259, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479378426, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_128BE6A580A94DC6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756864479378815, "dur": 431, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756864479379290, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1756864479379605, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479380829, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479381688, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479382692, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479383569, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479384769, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479385633, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479386542, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479387403, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479388294, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479388824, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479389661, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479390165, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479390309, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756864479390541, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756864479391159, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479391377, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479391504, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479391561, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479391617, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479391671, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479392315, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479392439, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479392612, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479393027, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479393305, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479393554, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479393622, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479394487, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479394561, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479395008, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479395303, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479395768, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756864479396262, "dur": 203475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479367196, "dur": 10498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479377703, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_05BE74699551E17D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756864479377846, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479377908, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756864479378052, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_AF9E01432509B98A.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756864479378113, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479378284, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5A56B4C00496EA36.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756864479378405, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479378463, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5A56B4C00496EA36.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756864479378620, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756864479378828, "dur": 603, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1756864479379433, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756864479379523, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479379673, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479380738, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479381629, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479382517, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479383483, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479384852, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479385916, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479386876, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479387676, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479388493, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479388862, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479389658, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479390200, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479390344, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479391318, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479391386, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479391667, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479392351, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479392432, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479392611, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479393021, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479393160, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479393309, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479393591, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479394562, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479394995, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479395225, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479395721, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479395777, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756864479396252, "dur": 203513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479367225, "dur": 10482, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479377716, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_6A4766F7572387DA.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756864479377826, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_B16CC6C88F056D62.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756864479377876, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479377969, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_94137FF513C48ABF.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756864479378035, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479378109, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2731044146C71882.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756864479378292, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479378437, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756864479378627, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_18E07CEE712070C7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756864479379107, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756864479379232, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479379285, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1756864479379658, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479380721, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479381633, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479382547, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479383430, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479384348, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479385615, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479386481, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479387566, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479388391, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479388868, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479389625, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479390201, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479390356, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479391295, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479391390, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479391547, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479391660, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479392121, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479392207, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479392268, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479392320, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479392431, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479392640, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479393043, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479393348, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479393608, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479394531, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479394994, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479395216, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479395718, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479395771, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756864479396236, "dur": 203510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479367253, "dur": 10466, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479377821, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EA0B5218CB807CE8.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756864479378141, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_7D055410D6C309E4.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756864479378286, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479378433, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756864479378572, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_35E2FC16174B6AD9.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756864479378757, "dur": 519, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_097DDEAFECA28810.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756864479379392, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756864479379538, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479379593, "dur": 470, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756864479380064, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479381160, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479381991, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479382894, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479384694, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\EditorBinding\\IInspectableAttribute.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756864479383737, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479385533, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479386382, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479387573, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479388330, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479388830, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479389631, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479390169, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479390337, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479391296, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479391378, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479391542, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479391710, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479392150, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479392202, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756864479392423, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756864479392897, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479393080, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756864479393151, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479393353, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479393571, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479394481, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479394567, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479395010, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479395296, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479395766, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756864479396261, "dur": 203517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479367285, "dur": 10448, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479377745, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_30460E0C3839B364.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756864479377869, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_3F0948DC6B40CBC0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756864479378148, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3A6F5DE8BB04652D.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756864479378268, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B76ACB483990A7B3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756864479378396, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479378554, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756864479378751, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1756864479378884, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756864479379023, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756864479379220, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1756864479379670, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479380820, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479381978, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479382984, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479384012, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479385279, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479386136, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479387005, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479387869, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479388535, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479388853, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479389650, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479390190, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479390320, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756864479390546, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479390609, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756864479391487, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479391691, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479392307, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479392451, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479392616, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479393041, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479393345, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479393569, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479394559, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479395037, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479395203, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479395752, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479395810, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756864479396242, "dur": 203510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479367317, "dur": 10427, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479377752, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_81ADD57237B7A215.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479377827, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479378094, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_47278636E8915295.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479378181, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479378393, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F9E2212BE69EFFE3.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479378600, "dur": 524, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479379131, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479379251, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479379338, "dur": 9356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756864479388695, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479388888, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479388997, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756864479389516, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479389692, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479389786, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756864479390042, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479390287, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479390535, "dur": 1473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756864479392008, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479392207, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479392423, "dur": 1020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756864479393444, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479393617, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479393715, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756864479394377, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479394540, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756864479394636, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756864479394889, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479395046, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479395185, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479395752, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756864479396273, "dur": 203508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479367345, "dur": 10413, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479377767, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_C2B2B5EFE7050494.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479377879, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479378287, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479378549, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D8EA2BB5196147D6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479378624, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479378704, "dur": 504, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_EF1B9BDC36CA0637.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479379222, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479379380, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479379755, "dur": 8309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756864479388066, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479388274, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479388811, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479389621, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479390160, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479390295, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479390496, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479390553, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756864479391310, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479391585, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479391792, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756864479392245, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479392449, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756864479392631, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756864479393022, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479393150, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479393343, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479393584, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479394549, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479395021, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479395247, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479395781, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756864479396253, "dur": 203520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479367371, "dur": 10405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479377789, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B03107865E80E25E.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756864479377872, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479378102, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D69F050D9A0ED123.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756864479378172, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479378297, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D69F050D9A0ED123.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756864479378358, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756864479378539, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756864479378704, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1756864479379030, "dur": 740, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1756864479379772, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479381080, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479382235, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\RemoveDictionaryItem.cs"}}, {"pid": 12345, "tid": 23, "ts": 1756864479381940, "dur": 2132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479384073, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479385787, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479386661, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479387497, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479388488, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479388845, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479389646, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479390186, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479390317, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756864479390559, "dur": 855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756864479391415, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479391700, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479392324, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479392425, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479392596, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479393015, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479393099, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479393401, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479393576, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479394547, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479395019, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479395255, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479395786, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756864479396231, "dur": 203503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479366882, "dur": 10703, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479377598, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756864479378106, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479378174, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756864479378425, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479378486, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756864479378772, "dur": 491, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756864479379309, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1756864479379444, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756864479379542, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756864479379608, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479380663, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479381554, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479382507, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479383433, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479384311, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479385553, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479386403, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479387290, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479388420, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479388841, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479389639, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479390179, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479390320, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756864479390578, "dur": 1565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756864479392144, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479392449, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756864479392634, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756864479393024, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479393141, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479393366, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479393577, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479394494, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479394558, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479395025, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479395221, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479395724, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479395790, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756864479396245, "dur": 203512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756864479605221, "dur": 1676, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 45648, "tid": 638, "ts": 1756864479620919, "dur": 2260, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 45648, "tid": 638, "ts": 1756864479623230, "dur": 1840, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 45648, "tid": 638, "ts": 1756864479617065, "dur": 9076, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}