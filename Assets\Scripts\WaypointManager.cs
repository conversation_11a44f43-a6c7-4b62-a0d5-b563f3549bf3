using System.Collections.Generic;
using UnityEngine;
using Unity.Collections;
using Unity.Jobs;
#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

namespace ColdVor.RTS
{
    /// Manages waypoint system for RTS units
    /// Allows queuing multiple movement commands with visual indicators

    public class WaypointManager : MonoBehaviour
    {
        [Header("Waypoint Settings")]
        [SerializeField] private GameObject waypointPrefab;
        [SerializeField] private Color waypointColor = Color.cyan;
        [SerializeField] private float waypointScale = 1f;

        [Header("Input Settings")]
        [SerializeField] private KeyCode queueWaypointKey = KeyCode.LeftShift;

        [Header("Line Settings")]
        [SerializeField] private float lineWidth = 0.8f;
        [SerializeField] private float lineElevation = 1f;

        [Header("Patrol Settings")]
        [SerializeField] private Color patrolWaypointColor = new Color(1f, 1f, 0.6f, 0.9f); // Muted yellow
        [SerializeField] private Color patrolLineColor = new Color(1f, 0.6f, 0.2f, 0.8f); // Muted orange

        // Public accessors for colors
        public Color PatrolLineColor => patrolLineColor;
        public Color WaypointColor => waypointColor;

        // Public accessors for other classes
        public float LineWidth => lineWidth;
        public float LineElevation => lineElevation;

        private static WaypointManager instance;
        public static WaypointManager Instance => instance;

        private Dictionary<Unit, WaypointQueue> unitWaypoints = new Dictionary<Unit, WaypointQueue>();
        private Camera playerCamera;
        private PathfindingManager pathfindingManager;

        // Waypoint creation state
        private bool isCreatingWaypoints = false;
        private List<Unit> selectedUnitsForWaypoints = new List<Unit>();
        private List<Vector3> previewWaypoints = new List<Vector3>();

        // Patrol mode state
        private bool isInPatrolMode = false;
        private List<Unit> patrolUnits = new List<Unit>();
        private List<Vector3> patrolWaypoints = new List<Vector3>();



        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }

            playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindFirstObjectByType<Camera>();

            CreateDefaultWaypointPrefab();
        }

        private void Start()
        {
            pathfindingManager = FindFirstObjectByType<PathfindingManager>();

            // Subscribe to centralized input events
            if (RTSInputManager.Instance != null)
            {
                RTSInputManager.Instance.OnRightClickWaypoint += HandleRightClickWaypoint;
                RTSInputManager.Instance.OnPatrolWaypoint += HandlePatrolWaypoint;
                RTSInputManager.Instance.OnSelectionChanged += OnSelectionChanged;
            }
        }

        private void Update()
        {
            HandlePatrolModeInput();
            HandleWaypointPreview();
            UpdateWaypointQueues();
        }

        private void HandleRightClickWaypoint(Vector3 worldPosition)
        {
            if (RTSInputManager.Instance != null && RTSInputManager.Instance.HasSelection)
            {
                bool queueWaypoint = IsQueueKeyHeld();
                CreateWaypoint(RTSInputManager.Instance.SelectedUnits, worldPosition, queueWaypoint);
            }
        }

        private void HandlePatrolWaypoint(Vector3 worldPosition)
        {
            if (isInPatrolMode)
            {
                AddPatrolWaypoint(worldPosition);
            }
        }

        private void HandlePatrolModeInput()
        {
            bool patrolKeyHeld = IsPatrolModifierHeld();

            // Enter patrol mode
            if (patrolKeyHeld && !isInPatrolMode && RTSInputManager.Instance != null && RTSInputManager.Instance.HasSelection)
            {
                StartPatrolMode();
            }
            // Exit patrol mode and finalize route
            else if (!patrolKeyHeld && isInPatrolMode)
            {
                FinalizePatrolRoute();
            }

            // Patrol waypoint creation is now handled by HandlePatrolWaypoint event
        }

        private void HandleWaypointPreview()
        {
            // Handle waypoint creation mode for preview
            if (IsQueueKeyHeld() && RTSInputManager.Instance != null && RTSInputManager.Instance.HasSelection)
            {
                if (!isCreatingWaypoints)
                {
                    StartWaypointCreationMode();
                }

                // Show preview waypoint
                Vector3 mouseWorldPos = GetMouseWorldPosition();
                if (mouseWorldPos != Vector3.zero)
                {
                    UpdateWaypointPreview(mouseWorldPos);
                }
            }
            else if (isCreatingWaypoints)
            {
                EndWaypointCreationMode();
            }
        }

        private void StartWaypointCreationMode()
        {
            isCreatingWaypoints = true;
            selectedUnitsForWaypoints.Clear();
            previewWaypoints.Clear();

            // Store selected units for waypoint creation
            foreach (var selectable in RTSInputManager.Instance.SelectedUnits)
            {
                if (selectable is Unit unit)
                {
                    selectedUnitsForWaypoints.Add(unit);
                }
            }
        }

        private void UpdateWaypointPreview(Vector3 mousePosition)
        {
            // Update preview waypoint position
            if (previewWaypoints.Count == 0)
            {
                previewWaypoints.Add(mousePosition);
            }
            else
            {
                previewWaypoints[0] = mousePosition;
            }
        }

        private void EndWaypointCreationMode()
        {
            isCreatingWaypoints = false;
            selectedUnitsForWaypoints.Clear();
            previewWaypoints.Clear();
        }

        private Vector3 GetMouseWorldPosition()
        {
            Ray ray = playerCamera.ScreenPointToRay(GetMousePosition());

            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                return hit.point;
            }

            // Fallback to ground plane
            Plane groundPlane = new Plane(Vector3.up, Vector3.zero);
            if (groundPlane.Raycast(ray, out float distance))
            {
                return ray.GetPoint(distance);
            }

            return Vector3.zero;
        }

        public void CreateWaypoint(List<ISelectable> selectedUnits, Vector3 position, bool queueWaypoint = false)
        {
            List<Unit> units = new List<Unit>();
            foreach (var selectable in selectedUnits)
            {
                if (selectable is Unit unit && unit.CanMove)
                {
                    units.Add(unit);
                }
            }

            if (units.Count == 0) return;

            CreateWaypoint(units, position, queueWaypoint);
        }

        public void CreateWaypoint(List<Unit> units, Vector3 position, bool queueWaypoint = false)
        {
            if (units == null || units.Count == 0) return;

            // Validate position
            if (pathfindingManager != null)
            {
                position = pathfindingManager.GetNearestValidPosition(position);
            }

            foreach (Unit unit in units)
            {
                if (!unitWaypoints.ContainsKey(unit))
                {
                    unitWaypoints[unit] = new WaypointQueue(unit);
                }

                var waypointQueue = unitWaypoints[unit];

                if (!queueWaypoint)
                {
                    // Clear existing waypoints and create new one
                    waypointQueue.ClearWaypoints();
                }

                // Add new waypoint
                Waypoint newWaypoint = new Waypoint(position)
                {
                    waypointObject = CreateWaypointVisual(position),
                    isCompleted = false
                };

                waypointQueue.AddWaypoint(newWaypoint);

                // Set visibility based on current selection state
                bool isUnitSelected = RTSInputManager.Instance != null && RTSInputManager.Instance.SelectedUnits.Contains(unit);
                waypointQueue.SetVisibility(isUnitSelected);

                // Start movement if this is the first waypoint
                if (waypointQueue.waypoints.Count == 1)
                {
                    StartMovementToNextWaypoint(unit);
                }
            }

            // No visual updates - waypoints are completely static
        }

        private void StartPatrolMode()
        {
            isInPatrolMode = true;
            patrolUnits.Clear();
            patrolWaypoints.Clear();

            // Store selected units for patrol
            foreach (var selectable in RTSInputManager.Instance.SelectedUnits)
            {
                if (selectable is Unit unit && unit.CanMove)
                {
                    patrolUnits.Add(unit);

                    // Clear existing waypoints for preview
                    if (unitWaypoints.ContainsKey(unit))
                    {
                        unitWaypoints[unit].ClearWaypoints();
                        unitWaypoints.Remove(unit);
                    }
                }
            }
        }

        private void AddPatrolWaypoint(Vector3 position)
        {
            patrolWaypoints.Add(position);

            // Update patrol routes for all units using the proper waypoint system
            foreach (Unit unit in patrolUnits)
            {
                UpdatePatrolPreview(unit);
            }
        }

        private void UpdatePatrolPreview(Unit unit)
        {
            // Get or create waypoint queue for this unit
            if (!unitWaypoints.TryGetValue(unit, out WaypointQueue waypointQueue))
            {
                waypointQueue = new WaypointQueue(unit);
                unitWaypoints[unit] = waypointQueue;
            }

            // Clear existing preview waypoints
            waypointQueue.ClearWaypoints();
            waypointQueue.isPatrolRoute = true; // Mark as patrol but don't start movement yet
            waypointQueue.UpdateLineColors(); // Update colors to patrol colors

            // Add all current patrol waypoints using the proper system
            for (int i = 0; i < patrolWaypoints.Count; i++)
            {
                Waypoint newWaypoint = new Waypoint(patrolWaypoints[i])
                {
                    waypointObject = CreateWaypointVisual(patrolWaypoints[i], true), // true = patrol waypoint
                    isCompleted = false
                };

                waypointQueue.AddWaypoint(newWaypoint);
            }

            // Calculate patrol-specific path (terrain-only, no unit position dependency)
            if (waypointQueue.waypoints.Count > 0)
            {
                waypointQueue.CalculatePatrolPath();
            }

            // Set visibility based on current selection state
            bool isUnitSelected = RTSInputManager.Instance != null && RTSInputManager.Instance.SelectedUnits.Contains(unit);
            waypointQueue.SetVisibility(isUnitSelected);
        }

        private void FinalizePatrolRoute()
        {
            if (patrolWaypoints.Count > 0 && patrolUnits.Count > 0)
            {
                // Start movement for all patrol units (routes are already created)
                foreach (Unit unit in patrolUnits)
                {
                    if (unitWaypoints.ContainsKey(unit) && unitWaypoints[unit].waypoints.Count > 0)
                    {
                        StartMovementToNextWaypoint(unit);
                    }
                }
            }

            // Clean up patrol mode
            ClearPatrolMode();
        }

        private void ClearPatrolMode()
        {
            isInPatrolMode = false;
            patrolUnits.Clear();
            patrolWaypoints.Clear();
        }



        public void BreakPatrolRoutes(List<Unit> units)
        {
            if (units == null || units.Count == 0) return;

            foreach (Unit unit in units)
            {
                if (unitWaypoints.TryGetValue(unit, out WaypointQueue waypointQueue))
                {
                    if (waypointQueue.isPatrolRoute)
                    {
                        // Clear the patrol route and reset to normal waypoint mode
                        waypointQueue.ClearWaypoints();
                        waypointQueue.isPatrolRoute = false;
                        waypointQueue.UpdateLineColors(); // Add this line!

                        // Stop the unit's current movement
                        unit.StopMovement();
                    }
                }
            }
        }

        public GameObject CreateWaypointVisual(Vector3 position, bool isPatrolWaypoint = false)
        {
            GameObject waypointObj;

            // Ensure waypoint is elevated above terrain to avoid clipping
            Vector3 elevatedPosition = GetElevatedWaypointPosition(position);

            if (waypointPrefab != null)
            {
                waypointObj = Instantiate(waypointPrefab, elevatedPosition, Quaternion.identity);

                // Set color for instantiated prefab (create material instance to avoid shared material issues)
                var renderer = waypointObj.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material = new Material(renderer.material); // Create instance
                    renderer.material.color = isPatrolWaypoint ? patrolWaypointColor : waypointColor;
                }
            }
            else
            {
                waypointObj = CreateDefaultWaypointVisual(elevatedPosition, isPatrolWaypoint);
            }

            waypointObj.transform.localScale = Vector3.one * waypointScale;



            // Set initial visibility based on current selection state
            // Waypoints should only be visible if created for a selected unit
            waypointObj.SetActive(false); // Start hidden, will be shown by SetVisibility if unit is selected

            return waypointObj;
        }

        private Vector3 GetElevatedWaypointPosition(Vector3 basePosition)
        {
            // Always raycast down from above to find the actual ground level
            Vector3 rayStart = new Vector3(basePosition.x, basePosition.y + 50f, basePosition.z);
            Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 100f);

            // Place waypoint at configured elevation
            return new Vector3(basePosition.x, hit.point.y + lineElevation, basePosition.z);
        }

        private GameObject CreateDefaultWaypointVisual(Vector3 elevatedPosition, bool isPatrolWaypoint = false)
        {
            GameObject waypointObj = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            waypointObj.name = "Waypoint";
            waypointObj.transform.position = elevatedPosition; // Position is already elevated
            waypointObj.transform.localScale = new Vector3(0.5f, 0.1f, 0.5f);

            // Remove collider to prevent interference
            Destroy(waypointObj.GetComponent<Collider>());

            // Set material color based on waypoint type (create material instance)
            var renderer = waypointObj.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material = new Material(renderer.material); // Create instance
                renderer.material.color = isPatrolWaypoint ? patrolWaypointColor : waypointColor;
            }

            return waypointObj;
        }

        private void CreateDefaultWaypointPrefab()
        {
            if (waypointPrefab == null)
            {
                // Create a simple waypoint prefab
                waypointPrefab = CreateDefaultWaypointVisual(Vector3.zero);
                waypointPrefab.SetActive(false);
                DontDestroyOnLoad(waypointPrefab);
            }
        }

        private void StartMovementToNextWaypoint(Unit unit)
        {
            if (!unitWaypoints.ContainsKey(unit)) return;

            var waypointQueue = unitWaypoints[unit];
            var nextWaypoint = waypointQueue.GetCurrentWaypoint();

            if (nextWaypoint != null)
            {
                if (pathfindingManager != null)
                {
                    pathfindingManager.MoveUnit(unit, nextWaypoint.position);
                }
                else
                {
                    unit.MoveTo(nextWaypoint.position);
                }
            }
        }

        private void UpdateWaypointQueues()
        {
            List<Unit> unitsToRemove = new List<Unit>();

            foreach (var kvp in unitWaypoints)
            {
                Unit unit = kvp.Key;
                WaypointQueue waypointQueue = kvp.Value;

                if (unit == null)
                {
                    unitsToRemove.Add(unit);
                    continue;
                }

                // Update line progression as unit moves
                waypointQueue.UpdateLineProgression();

                var currentWaypoint = waypointQueue.GetCurrentWaypoint();
                if (currentWaypoint != null && !unit.IsMoving())
                {
                    float arrivalThreshold = GetUnitArrivalThreshold(unit);
                    float distanceToWaypoint = Vector3.Distance(unit.transform.position, currentWaypoint.position);

                    if (distanceToWaypoint < arrivalThreshold)
                    {
                        waypointQueue.CompleteCurrentWaypoint();

                        var nextWaypoint = waypointQueue.GetCurrentWaypoint();
                        if (nextWaypoint != null)
                        {
                            StartMovementToNextWaypoint(unit);
                        }
                        else
                        {
                            waypointQueue.ClearWaypoints();
                            unitsToRemove.Add(unit);
                        }
                    }
                }
            }

            foreach (Unit unit in unitsToRemove)
            {
                if (unitWaypoints.ContainsKey(unit))
                {
                    unitWaypoints[unit].ClearWaypoints();
                    unitWaypoints.Remove(unit);
                }
            }
        }



        private void OnSelectionChanged(List<ISelectable> selectedUnits)
        {
            // Show waypoints for selected units
            foreach (var kvp in unitWaypoints)
            {
                bool isSelected = false;
                foreach (var selectable in selectedUnits)
                {
                    if (ReferenceEquals(selectable, kvp.Key))
                    {
                        isSelected = true;
                        break;
                    }
                }

                kvp.Value.SetVisibility(isSelected);
            }
        }

        public void ClearWaypoints(Unit unit)
        {
            if (unitWaypoints.ContainsKey(unit))
            {
                unitWaypoints[unit].ClearWaypoints();
                unitWaypoints.Remove(unit);
            }
        }

        public void ClearAllWaypoints()
        {
            foreach (var waypointQueue in unitWaypoints.Values)
            {
                waypointQueue.ClearWaypoints();
            }
            unitWaypoints.Clear();
        }

        public List<Vector3> GetWaypoints(Unit unit)
        {
            if (unitWaypoints.ContainsKey(unit))
            {
                return unitWaypoints[unit].GetWaypointPositions();
            }
            return new List<Vector3>();
        }

        private float GetUnitArrivalThreshold(Unit unit)
        {
            if (unit == null) return 1f;

            // Use the unit's collider to determine arrival threshold
            Collider unitCollider = unit.GetComponent<Collider>();
            if (unitCollider != null)
            {
                // Use the largest dimension of the collider bounds + some buffer
                Vector3 size = unitCollider.bounds.size;
                float maxDimension = Mathf.Max(size.x, size.z); // Width or depth
                return maxDimension * 0.75f + 0.5f; // 75% of size + buffer
            }

            // Fallback: try NavMeshAgent radius
            if (unit.NavAgent != null)
            {
                return unit.NavAgent.radius * 2f + 0.5f;
            }

            // Final fallback
            return 1.5f;
        }





        private bool IsPatrolModifierHeld()
        {
#if ENABLE_INPUT_SYSTEM
            var keyboard = Keyboard.current;
            return keyboard != null && keyboard.leftCtrlKey.isPressed;
#else
            return Input.GetKey(KeyCode.LeftControl);
#endif
        }

        private bool IsQueueKeyHeld()
        {
#if ENABLE_INPUT_SYSTEM
            var keyboard = Keyboard.current;
            if (keyboard == null) return false;

            switch (queueWaypointKey)
            {
                case KeyCode.LeftShift:
                    return keyboard.leftShiftKey.isPressed;
                case KeyCode.RightShift:
                    return keyboard.rightShiftKey.isPressed;
                case KeyCode.LeftControl:
                    return keyboard.leftCtrlKey.isPressed;
                case KeyCode.RightControl:
                    return keyboard.rightCtrlKey.isPressed;
                default:
                    return Input.GetKey(queueWaypointKey);
            }
#else
            return Input.GetKey(queueWaypointKey);
#endif
        }

        private Vector3 GetMousePosition()
        {
#if ENABLE_INPUT_SYSTEM
            return Mouse.current != null ? Mouse.current.position.ReadValue() : Vector3.zero;
#else
            return Input.mousePosition;
#endif
        }

        private void OnDestroy()
        {
            if (RTSInputManager.Instance != null)
            {
                RTSInputManager.Instance.OnRightClickWaypoint -= HandleRightClickWaypoint;
                RTSInputManager.Instance.OnPatrolWaypoint -= HandlePatrolWaypoint;
                RTSInputManager.Instance.OnSelectionChanged -= OnSelectionChanged;
            }
        }
    }

    /// <summary>
    /// Represents a single waypoint
    /// </summary>
    [System.Serializable]
    public class Waypoint
    {
        public Vector3 position;
        public GameObject waypointObject;
        public GameObject circleMarker;
        public bool isCompleted;
        public WaypointType type = WaypointType.Move;

        public Waypoint(Vector3 pos)
        {
            position = pos;
            CreateCircleMarker();
        }

        private void CreateCircleMarker()
        {
            circleMarker = new GameObject("WaypointCircle");
            circleMarker.transform.position = position;

            var waypointManager = WaypointManager.Instance;
            float elevation = waypointManager != null ? waypointManager.LineElevation : 1f;

            if (type == WaypointType.Move)
            {
                // Muted colors for comfortable viewing
                Color mutedBlue = new Color(0.4f, 0.6f, 0.8f, 0.8f); // Soft blue-gray
                Color mutedGreen = new Color(0.5f, 0.7f, 0.5f, 0.8f); // Soft green
                CreateCircleRing(circleMarker, 0.9f, 0.1f, mutedBlue, elevation, "OuterRim");
                CreateCircleRing(circleMarker, 0.8f, 0.15f, mutedBlue, elevation, "OuterCircle");
                CreateCircleRing(circleMarker, 0.6f, 0.2f, mutedGreen, elevation, "InnerCircle");
                CreateCircleRing(circleMarker, 0.4f, 0.25f, mutedGreen, elevation, "CenterCircle");
            }
            else
            {
                Color mutedOrange = new Color(1f, 0.6f, 0.2f, 0.8f); // Soft orange
                Color mutedYellow = new Color(1f, 1f, 0.6f, 0.8f); // Soft yellow
                CreateCircleRing(circleMarker, 0.9f, 0.1f, mutedOrange, elevation, "OuterRim");
                CreateCircleRing(circleMarker, 0.8f, 0.15f, mutedOrange, elevation, "OuterCircle");
                CreateCircleRing(circleMarker, 0.6f, 0.2f, mutedYellow, elevation, "InnerCircle");
                CreateCircleRing(circleMarker, 0.4f, 0.25f, mutedYellow, elevation, "CenterCircle");
            }


        }

        private void CreateCircleRing(GameObject parent, float radius, float width, Color color, float elevation, string name)
        {
            GameObject ringObj = new GameObject(name);
            ringObj.transform.SetParent(parent.transform);

            LineRenderer ring = ringObj.AddComponent<LineRenderer>();
            Material ringMat = new Material(Shader.Find("Sprites/Default"));
            ringMat.color = color;
            ring.material = ringMat;
            ring.startWidth = width;
            ring.endWidth = width;
            ring.useWorldSpace = true;
            ring.loop = true;

            // Generate circle points at line elevation
            int segments = 32;
            Vector3[] points = new Vector3[segments];

            for (int i = 0; i < segments; i++)
            {
                float angle = i * 2f * Mathf.PI / segments;
                float x = Mathf.Cos(angle) * radius;
                float z = Mathf.Sin(angle) * radius;

                // Raycast to ground for each circle point
                Vector3 circlePoint = position + new Vector3(x, 0, z);
                Vector3 rayStart = new Vector3(circlePoint.x, circlePoint.y + 50f, circlePoint.z);
                Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 100f);

                // Use same elevation as the waypoint lines
                points[i] = new Vector3(circlePoint.x, hit.point.y + elevation, circlePoint.z);
            }

            ring.positionCount = segments;
            ring.SetPositions(points);
        }

        public void Destroy()
        {
            if (waypointObject != null)
            {
                Object.Destroy(waypointObject);
            }
            if (circleMarker != null)
            {
                Object.Destroy(circleMarker);
            }
        }
    }

    /// Manages a queue of waypoints for a single unit
    [System.Serializable]
    public class WaypointQueue
    {
        public Unit unit;
        public List<Waypoint> waypoints = new List<Waypoint>();
        public LineRenderer pathLine;
        public bool isPatrolRoute = false; // Whether this is a patrol route (infinite loop)
        private int currentWaypointIndex = 0;
        private Vector3[] staticPathPoints; // Store the static path once calculated
        private Vector3[] originalPathPoints; // Store original path for progressive hiding
        private Vector3 lastUnitPosition; // Track unit position to prevent unnecessary updates
        private int lastVisiblePointCount = 0; // Track visible point count for stability
        private int lastProgressIndex = 0; // Track progression along path to prevent jumping to future segments
        private bool isExtendingPath = false; // Prevent visual blips during path extension

        public WaypointQueue(Unit unit)
        {
            this.unit = unit;
            CreatePathLine();
        }

        private void CreatePathLine()
        {
            if (unit == null) return;

            GameObject lineObj = new GameObject("WaypointPath");
            // DO NOT parent to unit - keep line completely independent and static

            pathLine = lineObj.AddComponent<LineRenderer>();

            // Get colors based on patrol route status
            var manager = WaypointManager.Instance;
            Color lineColor = isPatrolRoute && manager != null ? manager.PatrolLineColor : Color.cyan;
            Color materialColor = isPatrolRoute ? new Color(1f, 0.6f, 0.2f, 0.9f) : new Color(0.4f, 0.6f, 0.8f, 0.9f); // Muted orange for patrol

            // Create optimized material for thick, smooth lines with consistent appearance
            Material lineMat = new Material(Shader.Find("Sprites/Default"));
            lineMat.color = materialColor;
            lineMat.SetFloat("_ZWrite", 0); // Disable depth writing for consistent appearance
            lineMat.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            lineMat.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            lineMat.SetInt("_ZTest", (int)UnityEngine.Rendering.CompareFunction.LessEqual);
            pathLine.material = lineMat;

            pathLine.startColor = lineColor;
            pathLine.endColor = lineColor;

            float lineWidth = manager != null ? manager.LineWidth : 0.15f;

            // Make lines thick for excellent visibility and smooth appearance
            pathLine.startWidth = lineWidth * 4.5f;
            pathLine.endWidth = lineWidth * 4.5f;
            pathLine.positionCount = 0;
            pathLine.useWorldSpace = true;

            // Enhanced smoothness settings for Unity 6.2 - maximum quality
            pathLine.numCapVertices = 32; // Maximum vertices for perfectly smooth caps
            pathLine.numCornerVertices = 32; // Maximum vertices for perfectly smooth corners
            pathLine.alignment = LineAlignment.View; // Always face camera for consistent appearance
            pathLine.textureMode = LineTextureMode.Stretch; // Better texture handling for thick lines
            pathLine.generateLightingData = false; // Disable lighting for consistent appearance

            // Disable shadows for performance
            pathLine.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
            pathLine.receiveShadows = false;
        }

        public void UpdateLineColors()
        {
            if (pathLine == null) return;

            // Get colors based on patrol route status
            var manager = WaypointManager.Instance;
            Color lineColor = isPatrolRoute && manager != null ? manager.PatrolLineColor : manager.WaypointColor;
            Color materialColor = isPatrolRoute ? new Color(1f, 0.6f, 0.2f, 0.9f) : new Color(0.4f, 0.6f, 0.8f, 0.9f);

            // Update line renderer colors
            pathLine.startColor = lineColor;
            pathLine.endColor = lineColor;

            // Update material color
            if (pathLine.material != null)
            {
                pathLine.material.color = materialColor;
            }
        }



        public void AddWaypoint(Waypoint waypoint)
        {
            waypoints.Add(waypoint);

            if (waypoints.Count == 1)
            {
                if (isPatrolRoute)
                {
                    CalculatePatrolPath(); // Use terrain-only path for patrol routes
                }
                else
                {
                    CalculateStaticPath(); // Use unit-based path for normal waypoints
                }
            }
            else
            {
                if (isPatrolRoute)
                {
                    CalculatePatrolPath(); // Recalculate entire patrol path
                }
                else
                {
                    ExtendPath(waypoint); // Extend normal path
                }
            }
        }

        public Waypoint GetCurrentWaypoint()
        {
            if (currentWaypointIndex < waypoints.Count)
            {
                return waypoints[currentWaypointIndex];
            }
            return null;
        }

        public void CompleteCurrentWaypoint()
        {
            var currentWaypoint = GetCurrentWaypoint();
            if (currentWaypoint != null)
            {
                currentWaypoint.isCompleted = true;
                //if not patrol, destroy the waypoint visual
                if (!isPatrolRoute)
                {
                    currentWaypoint.Destroy();
                }
                currentWaypointIndex++;

                // For patrol routes, restart from beginning when all waypoints completed
                if (isPatrolRoute && currentWaypointIndex >= waypoints.Count)
                {
                    currentWaypointIndex = 0;
                    // Reset all waypoints to not completed and recreate visuals
                    for (int i = 0; i < waypoints.Count; i++)
                    {
                        waypoints[i].isCompleted = false;
                        if (waypoints[i].waypointObject == null)
                        {
                            waypoints[i].waypointObject = WaypointManager.Instance.CreateWaypointVisual(waypoints[i].position, true); // true = patrol waypoint
                        }
                    }
                }

                // Update the path to remove completed segments when waypoint is completed
                if (!isPatrolRoute && currentWaypointIndex < waypoints.Count)
                {
                    UpdatePathAfterWaypointCompletion();
                }
            }
        }

        private void UpdatePathAfterWaypointCompletion()
        {
            if (unit == null || waypoints.Count == 0 || currentWaypointIndex >= waypoints.Count) return;

            // Recalculate the path from current unit position to remaining waypoints
            Vector3 currentPos = unit.transform.position;
            List<Vector3> newPathPositions = new List<Vector3>();

            // Calculate path from current position to next waypoint
            Vector3 nextWaypointPos = waypoints[currentWaypointIndex].position;

            UnityEngine.AI.NavMeshPath navPath = new UnityEngine.AI.NavMeshPath();
            if (UnityEngine.AI.NavMesh.CalculatePath(currentPos, nextWaypointPos, UnityEngine.AI.NavMesh.AllAreas, navPath))
            {
                // Add path segments with elevation sampling
                for (int i = 0; i < navPath.corners.Length - 1; i++)
                {
                    AddElevationSampledSegment(navPath.corners[i], navPath.corners[i + 1], newPathPositions);
                }

                // Add final corner with proper elevation
                if (navPath.corners.Length > 0)
                {
                    Vector3 finalCorner = navPath.corners[navPath.corners.Length - 1];
                    Vector3 elevatedFinal = GetElevatedPoint(finalCorner);
                    newPathPositions.Add(elevatedFinal);
                }
            }

            // Add paths to any remaining waypoints
            for (int i = currentWaypointIndex + 1; i < waypoints.Count; i++)
            {
                Vector3 fromPos = waypoints[i - 1].position;
                Vector3 toPos = waypoints[i].position;

                if (UnityEngine.AI.NavMesh.CalculatePath(fromPos, toPos, UnityEngine.AI.NavMesh.AllAreas, navPath))
                {
                    for (int j = 0; j < navPath.corners.Length - 1; j++)
                    {
                        AddElevationSampledSegment(navPath.corners[j], navPath.corners[j + 1], newPathPositions);
                    }

                    if (navPath.corners.Length > 0)
                    {
                        Vector3 finalCorner = navPath.corners[navPath.corners.Length - 1];
                        Vector3 elevatedFinal = GetElevatedPoint(finalCorner);
                        newPathPositions.Add(elevatedFinal);
                    }
                }
            }

            // Update the path arrays with the new remaining path
            staticPathPoints = newPathPositions.ToArray();
            originalPathPoints = newPathPositions.ToArray();

            // Reset progression tracking for the new path
            lastUnitPosition = currentPos;
            lastProgressIndex = 0;
            lastVisiblePointCount = staticPathPoints.Length;

            // Update the line renderer
            if (pathLine != null && staticPathPoints.Length > 0)
            {
                pathLine.positionCount = staticPathPoints.Length;
                pathLine.SetPositions(staticPathPoints);
            }
        }

        public void ClearWaypoints()
        {
            foreach (var waypoint in waypoints)
            {
                waypoint.Destroy();
            }
            waypoints.Clear();
            currentWaypointIndex = 0;
            staticPathPoints = null; // Clear static path
            originalPathPoints = null; // Clear original path
            lastProgressIndex = 0; // Reset progress tracking
            isPatrolRoute = false; // Reset patrol mode

            // Clear the line when waypoints are cleared
            if (pathLine != null)
            {
                pathLine.positionCount = 0;
            }
        }

        public void ResetWaypointIndex()
        {
            currentWaypointIndex = 0;
        }

        public List<Vector3> GetWaypointPositions()
        {
            List<Vector3> positions = new List<Vector3>();
            for (int i = currentWaypointIndex; i < waypoints.Count; i++)
            {
                positions.Add(waypoints[i].position);
            }
            return positions;
        }





        public void SetVisibility(bool visible)
        {
            foreach (var waypoint in waypoints)
            {
                if (waypoint.waypointObject != null)
                {
                    waypoint.waypointObject.SetActive(visible);
                }
            }

            if (pathLine != null)
            {
                pathLine.enabled = visible;
            }

            //also set visibility for circle markers
            foreach (var waypoint in waypoints)
            {
                if (waypoint.circleMarker != null)
                {
                    waypoint.circleMarker.SetActive(visible);
                }
            }



        }

        public void CalculatePatrolPath()
        {
            if (waypoints.Count == 0) return;

            List<Vector3> pathPositions = new List<Vector3>();

            // For patrol routes, create paths between waypoints only (no unit position dependency)
            for (int i = 0; i < waypoints.Count; i++)
            {
                Vector3 currentWaypoint = waypoints[i].position;
                Vector3 nextWaypoint = (i < waypoints.Count - 1) ? waypoints[i + 1].position : waypoints[0].position; // Loop back to start

                // Calculate NavMesh path between waypoints
                UnityEngine.AI.NavMeshPath navPath = new UnityEngine.AI.NavMeshPath();
                if (UnityEngine.AI.NavMesh.CalculatePath(currentWaypoint, nextWaypoint, UnityEngine.AI.NavMesh.AllAreas, navPath))
                {
                    // Add ultra-dense sampled segments for smooth terrain following
                    for (int j = 0; j < navPath.corners.Length - 1; j++)
                    {
                        Vector3 segmentStart = navPath.corners[j];
                        Vector3 segmentEnd = navPath.corners[j + 1];

                        // Use the same elevation sampling as normal waypoints
                        AddElevationSampledSegment(segmentStart, segmentEnd, pathPositions);
                    }
                }
                else
                {
                    // Fallback to direct line if NavMesh fails
                    AddElevationSampledSegment(currentWaypoint, nextWaypoint, pathPositions);
                }
            }

            // Store the static path
            staticPathPoints = pathPositions.ToArray();
            originalPathPoints = pathPositions.ToArray();

            // Set the line renderer
            if (pathLine != null && staticPathPoints.Length > 0)
            {
                pathLine.positionCount = staticPathPoints.Length;
                pathLine.SetPositions(staticPathPoints);
            }

            // Ensure patrol colors are applied
            UpdateLineColors();
        }



        private void CalculateStaticPath()
        {
            if (waypoints.Count == 0 || unit == null) return;

            List<Vector3> pathPositions = new List<Vector3>();

            // Start from unit's current position
            Vector3 currentPos = unit.transform.position;

            // Create path from unit to all waypoints
            for (int i = 0; i < waypoints.Count; i++)
            {
                Vector3 targetPos = waypoints[i].position;

                // Calculate NavMesh path from current position to this waypoint
                UnityEngine.AI.NavMeshPath navPath = new UnityEngine.AI.NavMeshPath();
                if (UnityEngine.AI.NavMesh.CalculatePath(currentPos, targetPos, UnityEngine.AI.NavMesh.AllAreas, navPath))
                {
                    // Add ultra-dense sampled segments to prevent any clipping
                    for (int j = 0; j < navPath.corners.Length - 1; j++)
                    {
                        Vector3 segmentStart = navPath.corners[j];
                        Vector3 segmentEnd = navPath.corners[j + 1];
                        // Use batch raycast sampling for anti-clipping
                        AddElevationSampledSegment(segmentStart, segmentEnd, pathPositions);
                    }

                    // Add final corner with proper elevation
                    if (navPath.corners.Length > 0)
                    {
                        Vector3 finalCorner = navPath.corners[navPath.corners.Length - 1];
                        Vector3 elevatedFinal = GetElevatedPoint(finalCorner);
                        pathPositions.Add(elevatedFinal);
                    }
                }

                // Update current position for next segment
                currentPos = targetPos;
            }

            // Store the static path - this will NEVER be recalculated
            staticPathPoints = pathPositions.ToArray();
            originalPathPoints = pathPositions.ToArray(); // Store original for progressive hiding

            // Initialize tracking variables for stable line progression
            lastUnitPosition = unit.transform.position;
            lastVisiblePointCount = staticPathPoints.Length;
            lastProgressIndex = 0; // Start at beginning of path

            // Set the line renderer ONCE and never touch it again
            if (pathLine != null && staticPathPoints.Length > 0)
            {
                pathLine.positionCount = staticPathPoints.Length;
                pathLine.SetPositions(staticPathPoints);
            }
        }

        private void ExtendPath(Waypoint newWaypoint)
        {
            if (waypoints.Count < 2) return;

            // Prevent visual blips during path extension
            isExtendingPath = true;

            // Temporarily hide the line to prevent showing old path
            if (pathLine != null)
            {
                pathLine.positionCount = 0;
            }

            // If no existing path, just proceed with extending from last waypoint
            // No fallbacks to unit position - always use waypoint-to-waypoint paths

            // Get the last waypoint position (previous waypoint)
            Vector3 lastWaypointPos = waypoints[waypoints.Count - 2].position;
            Vector3 newWaypointPos = newWaypoint.position;

            // Calculate NavMesh path from last waypoint to new waypoint
            UnityEngine.AI.NavMeshPath navPath = new UnityEngine.AI.NavMeshPath();
            if (UnityEngine.AI.NavMesh.CalculatePath(lastWaypointPos, newWaypointPos, UnityEngine.AI.NavMesh.AllAreas, navPath))
            {
                List<Vector3> newPathSegment = new List<Vector3>();

                // Add ultra-dense sampled segments for the new path extension
                for (int j = 0; j < navPath.corners.Length - 1; j++)
                {
                    Vector3 segmentStart = navPath.corners[j];
                    Vector3 segmentEnd = navPath.corners[j + 1];

                    // Use batch raycast sampling for all segments to prevent clipping
                    AddElevationSampledSegment(segmentStart, segmentEnd, newPathSegment);
                }

                // If we have existing static path points, extend from them
                if (staticPathPoints != null && staticPathPoints.Length > 0)
                {
                    // Only combine the REMAINING (uncompleted) portion of the existing path
                    // This prevents showing completed progression when extending
                    List<Vector3> remainingPath = new List<Vector3>();

                    // Find the unit's current position on the path and start from there
                    Vector3 unitPos = unit.transform.position;
                    int startIndex = 0;

                    // Find the closest point on the existing path to start from
                    float closestDistance = float.MaxValue;
                    for (int i = 0; i < staticPathPoints.Length; i++)
                    {
                        float distance = Vector3.Distance(unitPos, staticPathPoints[i]);
                        if (distance < closestDistance)
                        {
                            closestDistance = distance;
                            startIndex = i;
                        }
                    }

                    // Add remaining path points from the unit's current position
                    for (int i = startIndex; i < staticPathPoints.Length; i++)
                    {
                        remainingPath.Add(staticPathPoints[i]);
                    }

                    // Add the new segment
                    remainingPath.AddRange(newPathSegment);

                    // Update the static path with only the remaining portion
                    staticPathPoints = remainingPath.ToArray();
                    originalPathPoints = remainingPath.ToArray();
                }
                else
                {
                    // No existing path, just use the new segment
                    staticPathPoints = newPathSegment.ToArray();
                    originalPathPoints = newPathSegment.ToArray();
                }

                // Reset progression tracking for the new path that starts from current position
                // Since we removed the completed portion, start fresh
                lastUnitPosition = unit.transform.position;
                lastProgressIndex = 0; // Reset since we're starting from a new path
                lastVisiblePointCount = staticPathPoints.Length;

                // Update the line renderer
                if (pathLine != null && staticPathPoints.Length > 0)
                {
                    pathLine.positionCount = staticPathPoints.Length;
                    pathLine.SetPositions(staticPathPoints);
                }
            }

            UpdateLineColors();

            // Re-enable line progression updates
            isExtendingPath = false;
        }



        public void UpdateLineProgression()
        {
            if (pathLine == null || originalPathPoints == null || originalPathPoints.Length == 0 || unit == null)
                return;

            // Skip progression for patrol routes - always show full route
            if (isPatrolRoute)
                return;

            Vector3 unitPosition = unit.transform.position;
            List<Vector3> visiblePoints = new List<Vector3>();

            // Find progression along path using sequential search from last known position
            float closestDistance = float.MaxValue;
            int progressIndex = lastProgressIndex;
            float progressT = 0f;
            Vector3 exactPositionOnPath = Vector3.zero;

            // Search forward from last known position (prevents jumping to future overlapping segments)
            // Always search the entire remaining path to ensure progression doesn't get stuck
            int searchStart = Mathf.Max(0, lastProgressIndex - 2);
            int searchEnd = originalPathPoints.Length - 1;

            for (int i = searchStart; i < searchEnd; i++)
            {
                Vector3 segmentStart = originalPathPoints[i];
                Vector3 segmentEnd = originalPathPoints[i + 1];

                // Project unit position onto this line segment
                Vector3 segmentDirection = segmentEnd - segmentStart;
                float segmentLength = segmentDirection.magnitude;

                if (segmentLength > 0.001f) // Avoid division by zero
                {
                    Vector3 unitToStart = unitPosition - segmentStart;
                    float projectionLength = Vector3.Dot(unitToStart, segmentDirection.normalized);

                    // Clamp to segment bounds
                    projectionLength = Mathf.Clamp(projectionLength, 0f, segmentLength);
                    float t = projectionLength / segmentLength;

                    // Calculate the exact point on the segment
                    Vector3 pointOnSegment = Vector3.Lerp(segmentStart, segmentEnd, t);

                    // Use 2D distance for ground-based comparison
                    Vector3 unitPos2D = new Vector3(unitPosition.x, pointOnSegment.y, unitPosition.z);
                    float distance = Vector3.Distance(unitPos2D, pointOnSegment);

                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        progressIndex = i;
                        progressT = t;
                        exactPositionOnPath = pointOnSegment;
                    }
                }
            }

            // Update progress tracking (only move forward, prevent jumping back)
            if (closestDistance < 15f && progressIndex >= lastProgressIndex)
            {
                lastProgressIndex = progressIndex;
            }

            // Build the visible path starting from the exact unit position
            if (closestDistance < 15f) // Only if unit is reasonably close to path (increased tolerance)
            {
                // Add the exact position where the unit is on the path
                visiblePoints.Add(exactPositionOnPath);

                // Add the remaining portion of the current segment
                if (progressT < 1f && progressIndex < originalPathPoints.Length - 1)
                {
                    Vector3 segmentEnd = originalPathPoints[progressIndex + 1];
                    visiblePoints.Add(segmentEnd);
                }

                // Add all subsequent path points
                for (int i = progressIndex + 2; i < originalPathPoints.Length; i++)
                {
                    visiblePoints.Add(originalPathPoints[i]);
                }
            }
            else
            {
                // If unit is far from path, show the entire remaining path
                for (int i = 0; i < originalPathPoints.Length; i++)
                {
                    visiblePoints.Add(originalPathPoints[i]);
                }
            }

            // Update the line renderer only if there's a significant change
            if (visiblePoints.Count > 0)
            {
                // Update more frequently but still prevent excessive updates
                if (Mathf.Abs(visiblePoints.Count - lastVisiblePointCount) > 1 || lastVisiblePointCount == 0)
                {
                    pathLine.positionCount = visiblePoints.Count;
                    pathLine.SetPositions(visiblePoints.ToArray());
                    lastVisiblePointCount = visiblePoints.Count;
                }
            }
            else
            {
                // Hide the line completely if no points remain
                if (pathLine.positionCount > 0)
                {
                    pathLine.positionCount = 0;
                    lastVisiblePointCount = 0;
                }
            }
        }



        private Vector3 GetElevatedPoint(Vector3 point)
        {
            var waypointManager = WaypointManager.Instance;
            float elevation = waypointManager != null ? waypointManager.LineElevation : 1f;

            Vector3 rayStart = new Vector3(point.x, point.y + 100f, point.z);
            Physics.Raycast(rayStart, Vector3.down, out RaycastHit groundHit, 150f, 1); // Only hit Default layer (ground)

            return new Vector3(point.x, groundHit.point.y + elevation, point.z);
        }

        private void AddElevationSampledSegment(Vector3 start, Vector3 end, List<Vector3> pathPositions)
        {
            var waypointManager = WaypointManager.Instance;
            float elevation = waypointManager != null ? waypointManager.LineElevation : 1f;



            float segmentLength = Vector3.Distance(start, end);
            // Ultra-dense sampling for long distances: 1 sample per 0.15 units, minimum 30 samples
            int samples = Mathf.Max(30, Mathf.RoundToInt(segmentLength * 6.67f));

            // Use batch raycasting for efficiency with large sample counts
            NativeArray<RaycastCommand> raycastCommands = new NativeArray<RaycastCommand>(samples + 1, Allocator.TempJob);
            NativeArray<RaycastHit> raycastResults = new NativeArray<RaycastHit>(samples + 1, Allocator.TempJob);

            // Setup raycast commands
            for (int i = 0; i <= samples; i++)
            {
                float t = i / (float)samples;
                Vector3 samplePoint = Vector3.Lerp(start, end, t);
                Vector3 rayStart = new Vector3(samplePoint.x, 100f, samplePoint.z);

                raycastCommands[i] = new RaycastCommand(rayStart, Vector3.down, new QueryParameters(
                    layerMask: 1, // Only hit Default layer (ground), not units
                    hitTriggers: QueryTriggerInteraction.Ignore
                ), 150f);
            }

            // Execute batch raycast
            JobHandle raycastJob = RaycastCommand.ScheduleBatch(raycastCommands, raycastResults, 32);
            raycastJob.Complete();

            // Process results
            for (int i = 0; i <= samples; i++)
            {
                float t = i / (float)samples;
                Vector3 samplePoint = Vector3.Lerp(start, end, t);



                // Use raycast result directly - no fallbacks
                float groundHeight = raycastResults[i].point.y;

                Vector3 elevatedPoint = new Vector3(samplePoint.x, groundHeight + elevation, samplePoint.z);



                // Add point if it's different enough from the last one (very tight tolerance)
                if (pathPositions.Count == 0 || Vector3.Distance(pathPositions[pathPositions.Count - 1], elevatedPoint) > 0.02f)
                {
                    pathPositions.Add(elevatedPoint);
                }
            }

            // Dispose native arrays
            raycastCommands.Dispose();
            raycastResults.Dispose();
        }


    }

    /// <summary>
    /// Types of waypoints
    /// </summary>
    public enum WaypointType
    {
        Move,           // Simple movement waypoint
        Attack,         // Attack move waypoint
        Patrol,         // Patrol waypoint
        Hold,           // Hold position waypoint
        Formation       // Formation waypoint
    }
}
