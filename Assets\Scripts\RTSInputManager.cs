using System.Collections.Generic;
using System.Linq;
using UnityEngine;
#if ENABLE_INPUT_SYSTEM
using UnityEngine.InputSystem;
#endif

namespace ColdVor.RTS
{
    /// <summary>
    /// Centralized input manager that handles all mouse input and routes it to appropriate systems
    /// Prevents conflicts between camera, selection, command, and waypoint systems
    /// </summary>
    public class RTSInputManager : MonoBehaviour
    {
        [Header("Input Timing")]
        [SerializeField] private float cameraRotationDelay = 0.2f; // Time before camera rotation takes priority
        [SerializeField] private float doubleClickTime = 0.3f;

        [Header("Selection Settings")]
        [SerializeField] private LayerMask selectableLayerMask = -1;
        [SerializeField] private bool allowMultiSelection = true;

        [Header("Selection Box")]
        [SerializeField] private RectTransform selectionBoxUI;
        [SerializeField] private Color selectionBoxColor = new Color(0.8f, 0.8f, 0.8f, 0.25f);

        [Header("Keyboard Commands")]
        [SerializeField] private KeyCode formationToggleKey = KeyCode.F;
        [SerializeField] private KeyCode stopCommandKey = KeyCode.X; // Changed from S to avoid camera movement conflict
        [SerializeField] private KeyCode holdPositionKey = KeyCode.H;
        [SerializeField] private KeyCode patrolModifierKey = KeyCode.LeftControl;

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = true;
        
        // Input state tracking
        private float rightMouseButtonPressTime = 0f;
        private bool isRightMouseButtonHeld = false;
        private bool rightMouseButtonConsumed = false;
        private Vector3 rightMouseButtonStartPosition;

        private bool isLeftMouseButtonHeld = false;
        private float leftMouseButtonPressTime = 0f;

        // Selection state
        private List<ISelectable> selectedUnits = new List<ISelectable>();
        private Camera playerCamera;
        private bool isSelecting = false;
        private Vector3 mouseStartPosition;
        private Vector3 mouseEndPosition;
        private float lastClickTime = 0f;
        private ISelectable lastClickedUnit = null;
        
        // System references (for debug info only)
        private RTSCameraController cameraController;
        private UnitCommandSystem commandSystem;
        private WaypointManager waypointManager;
        
        // Input events
        public System.Action<Vector3> OnRightClickCommand;
        public System.Action<Vector3> OnRightClickWaypoint;
        public System.Action<Vector3> OnPatrolWaypoint;
        public System.Action OnStartCameraRotation;
        public System.Action OnStopCameraRotation;

        // Keyboard command events (migrated from UnitCommandSystem)
        public System.Action OnStopCommand;
        public System.Action OnHoldPositionCommand;
        public System.Action OnFormationToggleCommand;

        // Selection events
        public System.Action<List<ISelectable>> OnSelectionChanged;

        // Selection properties
        public List<ISelectable> SelectedUnits => new List<ISelectable>(selectedUnits);
        public bool HasSelection => selectedUnits.Count > 0;

        // Input configuration properties
        public KeyCode PatrolModifierKey => patrolModifierKey;
        
        // Singleton pattern
        private static RTSInputManager instance;
        public static RTSInputManager Instance => instance;
        
        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            // Initialize camera
            playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindFirstObjectByType<Camera>();

            // Create selection box if needed
            if (selectionBoxUI == null)
                CreateSelectionBoxUI();
        }
        
        private void Start()
        {
            // Find system references
            cameraController = FindFirstObjectByType<RTSCameraController>();
            commandSystem = FindFirstObjectByType<UnitCommandSystem>();
            waypointManager = FindFirstObjectByType<WaypointManager>();

            if (showDebugInfo)
            {
                Debug.Log($"RTSInputManager initialized. Found systems: Camera={cameraController != null}, Commands={commandSystem != null}, Waypoints={waypointManager != null}");
            }
        }
        
        private void Update()
        {
            HandleRightMouseButton();
            HandleLeftMouseButton();
            UpdateSelectionBox();
            HandleKeyboardCommands();
        }
        
        private void HandleRightMouseButton()
        {
            // Track right mouse button press
            if (IsRightMouseButtonDown())
            {
                rightMouseButtonPressTime = Time.time;
                isRightMouseButtonHeld = true;
                rightMouseButtonConsumed = false;
                rightMouseButtonStartPosition = GetMousePosition();

                if (showDebugInfo)
                    Debug.Log("Right mouse button pressed");
            }
            
            // Check for camera rotation (after delay)
            if (isRightMouseButtonHeld && !rightMouseButtonConsumed)
            {
                float holdDuration = Time.time - rightMouseButtonPressTime;
                
                if (holdDuration >= cameraRotationDelay)
                {
                    // Start camera rotation
                    rightMouseButtonConsumed = true;
                    OnStartCameraRotation?.Invoke();
                    
                    if (showDebugInfo)
                        Debug.Log("Camera rotation started");
                }
            }
            
            // Handle right mouse button release
            if (IsRightMouseButtonUp())
            {
                float holdDuration = Time.time - rightMouseButtonPressTime;
                
                if (!rightMouseButtonConsumed && holdDuration < cameraRotationDelay)
                {
                    // Quick click - route to appropriate system
                    Vector3 worldPosition = GetMouseWorldPosition();
                    if (worldPosition != Vector3.zero)
                    {
                        RouteRightClick(worldPosition);
                    }
                }
                else if (rightMouseButtonConsumed)
                {
                    // Stop camera rotation
                    OnStopCameraRotation?.Invoke();
                    
                    if (showDebugInfo)
                        Debug.Log("Camera rotation stopped");
                }
                
                isRightMouseButtonHeld = false;
                rightMouseButtonConsumed = false;
            }
        }
        
        private void HandleLeftMouseButton()
        {
            // Left mouse button down - start selection
            if (IsLeftMouseButtonDown())
            {
                leftMouseButtonPressTime = Time.time;
                isLeftMouseButtonHeld = true;
                mouseStartPosition = GetMousePosition();
                mouseEndPosition = mouseStartPosition;
                isSelecting = true;

                if (selectionBoxUI != null)
                    selectionBoxUI.gameObject.SetActive(false);

                if (showDebugInfo)
                    Debug.Log("Left mouse button pressed - starting selection");
            }

            // Left mouse button held - update selection box
            if (IsLeftMouseButtonHeld() && isSelecting)
            {
                mouseEndPosition = GetMousePosition();

                // Show selection box if we've moved enough
                Vector3 difference = mouseEndPosition - mouseStartPosition;
                if (difference.magnitude > 10f && selectionBoxUI != null)
                {
                    selectionBoxUI.gameObject.SetActive(true);
                }
            }

            // Left mouse button up - complete selection
            if (IsLeftMouseButtonUp())
            {
                if (!isSelecting) return;

                isSelecting = false;
                if (selectionBoxUI != null)
                    selectionBoxUI.gameObject.SetActive(false);

                Vector3 difference = mouseEndPosition - mouseStartPosition;

                // Single click selection
                if (difference.magnitude <= 10f)
                {
                    HandleSingleSelection();
                }
                // Box selection
                else if (allowMultiSelection)
                {
                    HandleBoxSelection();
                }

                isLeftMouseButtonHeld = false;
            }
        }

        private void HandleSingleSelection()
        {
            Ray ray = playerCamera.ScreenPointToRay(mouseStartPosition);

            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, selectableLayerMask))
            {
                ISelectable clickedUnit = hit.collider.GetComponent<ISelectable>();

                if (clickedUnit != null && clickedUnit.CanBeSelected())
                {
                    bool isDoubleClick = (Time.time - lastClickTime) < doubleClickTime &&
                                       clickedUnit == lastClickedUnit;

                    if (isDoubleClick)
                    {
                        SelectAllOfSameType(clickedUnit);
                    }
                    else
                    {
                        bool addToSelection = IsShiftHeld() && allowMultiSelection;

                        if (addToSelection)
                        {
                            if (selectedUnits.Contains(clickedUnit))
                                RemoveFromSelection(clickedUnit);
                            else
                                AddToSelection(clickedUnit);
                        }
                        else
                        {
                            SetSelection(new List<ISelectable> { clickedUnit });
                        }
                    }

                    lastClickTime = Time.time;
                    lastClickedUnit = clickedUnit;
                }
                else
                {
                    if (showDebugInfo)
                        Debug.Log($"Hit non-selectable object: {hit.collider.name}");
                    if (!IsShiftHeld())
                        ClearSelection();
                }
            }
            else
            {
                if (showDebugInfo)
                    Debug.Log("No hit detected");
                if (!IsShiftHeld())
                    ClearSelection();
            }
        }

        private void HandleBoxSelection()
        {
            List<ISelectable> unitsInBox = GetUnitsInSelectionBox();

            if (IsShiftHeld())
            {
                // Add to existing selection
                foreach (var unit in unitsInBox)
                {
                    if (!selectedUnits.Contains(unit))
                        AddToSelection(unit);
                }
            }
            else
            {
                // Replace selection
                SetSelection(unitsInBox);
            }
        }

        private void RouteRightClick(Vector3 worldPosition)
        {
            bool isPatrolMode = IsPatrolModifierHeld();
            bool hasSelection = HasSelection;

            if (showDebugInfo)
                Debug.Log($"Routing right click: PatrolMode={isPatrolMode}, HasSelection={hasSelection}, Position={worldPosition}");

            if (isPatrolMode && hasSelection)
            {
                // Patrol waypoint
                OnPatrolWaypoint?.Invoke(worldPosition);
            }
            else if (IsQueueKeyHeld() && hasSelection)
            {
                // Queue waypoint
                OnRightClickWaypoint?.Invoke(worldPosition);
            }
            else if (hasSelection)
            {
                // Command
                OnRightClickCommand?.Invoke(worldPosition);
            }
        }
        
        // Public methods for systems to check input state
        public bool IsCameraRotating => isRightMouseButtonHeld && rightMouseButtonConsumed;
        public bool IsRightMouseHeld => isRightMouseButtonHeld;
        public bool IsLeftMouseHeld => isLeftMouseButtonHeld;
        
        // Input abstraction methods
        private bool IsRightMouseButtonDown()
        {
#if ENABLE_INPUT_SYSTEM
            return Mouse.current != null && Mouse.current.rightButton.wasPressedThisFrame;
#else
            return Input.GetMouseButtonDown(1);
#endif
        }
        
        private bool IsRightMouseButtonUp()
        {
#if ENABLE_INPUT_SYSTEM
            return Mouse.current != null && Mouse.current.rightButton.wasReleasedThisFrame;
#else
            return Input.GetMouseButtonUp(1);
#endif
        }
        
        private bool IsLeftMouseButtonDown()
        {
#if ENABLE_INPUT_SYSTEM
            return Mouse.current != null && Mouse.current.leftButton.wasPressedThisFrame;
#else
            return Input.GetMouseButtonDown(0);
#endif
        }
        
        private bool IsLeftMouseButtonUp()
        {
#if ENABLE_INPUT_SYSTEM
            return Mouse.current != null && Mouse.current.leftButton.wasReleasedThisFrame;
#else
            return Input.GetMouseButtonUp(0);
#endif
        }
        
        private bool IsPatrolModifierHeld()
        {
            return IsKeyHeld(patrolModifierKey);
        }
        
        private bool IsQueueKeyHeld()
        {
#if ENABLE_INPUT_SYSTEM
            var keyboard = Keyboard.current;
            return keyboard != null && (keyboard.leftShiftKey.isPressed || keyboard.rightShiftKey.isPressed);
#else
            return Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
#endif
        }

        private bool IsShiftHeld()
        {
#if ENABLE_INPUT_SYSTEM
            var keyboard = Keyboard.current;
            return keyboard != null && (keyboard.leftShiftKey.isPressed || keyboard.rightShiftKey.isPressed);
#else
            return Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
#endif
        }

        private bool IsLeftMouseButtonHeld()
        {
#if ENABLE_INPUT_SYSTEM
            return Mouse.current != null && Mouse.current.leftButton.isPressed;
#else
            return Input.GetMouseButton(0);
#endif
        }

        // General input methods (migrated from UnitCommandSystem)
        public bool IsKeyHeld(KeyCode key)
        {
#if ENABLE_INPUT_SYSTEM
            var keyboard = Keyboard.current;
            if (keyboard == null) return false;

            switch (key)
            {
                case KeyCode.LeftControl: return keyboard.leftCtrlKey.isPressed;
                case KeyCode.RightControl: return keyboard.rightCtrlKey.isPressed;
                case KeyCode.LeftShift: return keyboard.leftShiftKey.isPressed;
                case KeyCode.RightShift: return keyboard.rightShiftKey.isPressed;
                case KeyCode.LeftAlt: return keyboard.leftAltKey.isPressed;
                case KeyCode.RightAlt: return keyboard.rightAltKey.isPressed;
                case KeyCode.X: return keyboard.xKey.isPressed;
                case KeyCode.H: return keyboard.hKey.isPressed;
                case KeyCode.F: return keyboard.fKey.isPressed;
                default: return false; // Add more keys as needed
            }
#else
            return Input.GetKey(key);
#endif
        }

        public bool IsKeyDown(KeyCode key)
        {
#if ENABLE_INPUT_SYSTEM
            var keyboard = Keyboard.current;
            if (keyboard == null) return false;

            switch (key)
            {
                case KeyCode.X: return keyboard.xKey.wasPressedThisFrame;
                case KeyCode.H: return keyboard.hKey.wasPressedThisFrame;
                case KeyCode.F: return keyboard.fKey.wasPressedThisFrame;
                case KeyCode.LeftControl: return keyboard.leftCtrlKey.wasPressedThisFrame;
                case KeyCode.RightControl: return keyboard.rightCtrlKey.wasPressedThisFrame;
                case KeyCode.LeftShift: return keyboard.leftShiftKey.wasPressedThisFrame;
                case KeyCode.RightShift: return keyboard.rightShiftKey.wasPressedThisFrame;
                case KeyCode.LeftAlt: return keyboard.leftAltKey.wasPressedThisFrame;
                case KeyCode.RightAlt: return keyboard.rightAltKey.wasPressedThisFrame;
                default: return false;
            }
#else
            return Input.GetKeyDown(key);
#endif
        }

        public Vector3 GetMousePosition()
        {
#if ENABLE_INPUT_SYSTEM
            return Mouse.current != null ? Mouse.current.position.ReadValue() : Vector3.zero;
#else
            return Input.mousePosition;
#endif
        }

        private void HandleKeyboardCommands()
        {
            // Stop command
            if (IsKeyDown(stopCommandKey))
            {
                OnStopCommand?.Invoke();
            }

            // Hold position command
            if (IsKeyDown(holdPositionKey))
            {
                OnHoldPositionCommand?.Invoke();
            }

            // Formation toggle
            if (IsKeyDown(formationToggleKey))
            {
                OnFormationToggleCommand?.Invoke();
            }
        }
        
        private Vector3 GetMouseWorldPosition()
        {
            Camera playerCamera = Camera.main;
            if (playerCamera == null)
                playerCamera = FindFirstObjectByType<Camera>();
                
            if (playerCamera == null) return Vector3.zero;
            
            Ray ray = playerCamera.ScreenPointToRay(GetMousePosition());
            
            // Try terrain/ground first
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, 1))
            {
                return hit.point;
            }
            
            // Fallback to ground plane
            Plane groundPlane = new Plane(Vector3.up, Vector3.zero);
            if (groundPlane.Raycast(ray, out float distance))
            {
                return ray.GetPoint(distance);
            }
            
            return Vector3.zero;
        }

        // Selection management methods
        private List<ISelectable> GetUnitsInSelectionBox()
        {
            List<ISelectable> unitsInBox = new List<ISelectable>();
            ISelectable[] allSelectables = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                .OfType<ISelectable>().ToArray();

            Vector3 min = Vector3.Min(mouseStartPosition, mouseEndPosition);
            Vector3 max = Vector3.Max(mouseStartPosition, mouseEndPosition);

            foreach (var selectable in allSelectables)
            {
                if (!selectable.CanBeSelected()) continue;

                Vector3 worldPos = selectable.Transform.position;
                Vector3 screenPos = playerCamera.WorldToScreenPoint(worldPos);

                // Check if unit is in front of camera and within selection box
                if (screenPos.z > 0 && // In front of camera
                    screenPos.x >= min.x && screenPos.x <= max.x &&
                    screenPos.y >= min.y && screenPos.y <= max.y)
                {
                    // Additional raycast check to ensure unit is actually visible
                    Ray ray = playerCamera.ScreenPointToRay(screenPos);
                    if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, selectableLayerMask))
                    {
                        if (hit.collider.GetComponent<ISelectable>() == selectable)
                        {
                            unitsInBox.Add(selectable);
                        }
                    }
                }
            }

            return unitsInBox;
        }

        private void SelectAllOfSameType(ISelectable clickedUnit)
        {
            if (clickedUnit is Unit unit)
            {
                Unit[] allUnits = FindObjectsByType<Unit>(FindObjectsSortMode.None);
                List<ISelectable> sameTypeUnits = new List<ISelectable>();

                foreach (Unit u in allUnits)
                {
                    if (u.UnitType == unit.UnitType && u.CanBeSelected())
                    {
                        sameTypeUnits.Add(u);
                    }
                }

                SetSelection(sameTypeUnits);
            }
        }

        public void SetSelection(List<ISelectable> newSelection)
        {
            ClearSelection();
            foreach (var unit in newSelection)
            {
                AddToSelection(unit);
            }
        }

        public void AddToSelection(ISelectable unit)
        {
            if (unit != null && !selectedUnits.Contains(unit))
            {
                selectedUnits.Add(unit);
                unit.OnSelected();
                OnSelectionChanged?.Invoke(selectedUnits);
            }
        }

        public void RemoveFromSelection(ISelectable unit)
        {
            if (selectedUnits.Remove(unit))
            {
                unit.OnDeselected();
                OnSelectionChanged?.Invoke(selectedUnits);
            }
        }

        public void ClearSelection()
        {
            foreach (var unit in selectedUnits)
            {
                unit.OnDeselected();
            }
            selectedUnits.Clear();
            OnSelectionChanged?.Invoke(selectedUnits);
        }

        private void UpdateSelectionBox()
        {
            if (!isSelecting || selectionBoxUI == null || !selectionBoxUI.gameObject.activeInHierarchy)
                return;

            Vector3 start = mouseStartPosition;
            Vector3 end = mouseEndPosition;

            Vector3 center = (start + end) / 2f;
            Vector3 size = new Vector3(Mathf.Abs(end.x - start.x), Mathf.Abs(end.y - start.y), 0f);

            selectionBoxUI.position = center;
            selectionBoxUI.sizeDelta = size;
        }

        private void CreateSelectionBoxUI()
        {
            // Create selection box UI if it doesn't exist
            Canvas canvas = FindFirstObjectByType<Canvas>();
            if (canvas == null) return;

            GameObject selectionBox = new GameObject("SelectionBox");
            selectionBox.transform.SetParent(canvas.transform, false);

            selectionBoxUI = selectionBox.AddComponent<RectTransform>();
            var image = selectionBox.AddComponent<UnityEngine.UI.Image>();
            image.color = selectionBoxColor;

            selectionBox.SetActive(false);
        }

        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }
    }
}
